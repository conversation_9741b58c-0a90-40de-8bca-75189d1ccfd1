#set dotenv-required := true
#set dotenv-path := "set_env.sh"

docker_build:
	DOCKER_BUILDKIT=1 docker build \
		--build-arg UV_VERSION=$UV_VERSION \
		--build-arg UV_INDEX_DEFAULT_USERNAME=$UV_INDEX_DEFAULT_USERNAME \
		--build-arg UV_INDEX_DEFAULT_PASSWORD=$UV_INDEX_DEFAULT_PASSWORD \
		--build-arg GATEWAY_API_URL=$GATEWAY_API_URL \
		--build-arg CLIENT_ID=$CLIENT_ID \
		--build-arg CLIENT_SECRET=$CLIENT_SECRET \
		. -t jso-api:dev

docker_run:
	docker run -e CLIENT_ID=$CLIENT_ID \
		-e CLIENT_SECRET=$CLIENT_SECRET \
		-e GATEWAY_API_URL=$GATEWAY_API_URL \
		-e IDP_ROOT_URI="${IDP_ROOT_URI}" \
		jso-api:dev

lint:
	uv run ruff check .
	uv run ruff format . --check
	
start_server:
	uv run uvicorn api.main:api --port 8071 --reload

start_runners:
	uv run python3 -m api.runners.objects_cleanup &
	uv run python3 -m api.runners.buckets_cleanup &
	uv run python3 -m api.runners.backintime &

stop_runners:
	pkill -9 python3

tests:
	uv run python3 -m unittest -fv tests.test_cleanup.TestBucketsCleanupLock
	uv run python3 -m unittest -fv tests.test_cleanup.TestBucketsCleanup
	uv run python3 -m unittest -fv tests.test_cleanup.TestObjectsCleanup
	uv run python3 -m unittest -fv tests.test_vault_functional.TestVault
	uv run python3 -m unittest -fv tests.test_console_functional.TestConsole
	uv run python3 -m unittest -fv tests.test_metrics_functional.TestMetrics

test-cleanup:
	uv run python3 -m unittest -fv tests.test_cleanup.TestBucketsCleanupLock
	uv run python3 -m unittest -fv tests.test_cleanup.TestBucketsCleanup
	uv run python3 -m unittest -fv tests.test_cleanup.TestObjectsCleanup

test-functional:
	uv run python3 -m unittest -fv tests.test_vault_functional.TestVault
	uv run python3 -m unittest -fv tests.test_console_functional.TestConsole
	uv run python3 -m unittest -fv tests.test_metrics_functional.TestMetrics

# Provider abstraction unit tests
test-providers:
	uv run python3 tests/run_provider_tests.py

# Run specific provider test class
test-provider-class CLASS:
	uv run python3 tests/run_provider_tests.py {{CLASS}}

# Run provider tests with pytest (alternative method)
test-providers-pytest:
	PYTHONPATH=. uv run pytest tests/test_providers.py -v --tb=short
