import logging
import time
from functools import wraps
from typing import Callable, Dict, List, Optional

import scality.exceptions  # type: ignore
from fastapi import (  # type: ignore
    APIRouter,
    Depends,
    HTTPException,
    Path,
    Query,
    Response,
    status,
)
from fastapi.responses import JSONResponse  # type: ignore
from fp_aaa_kit import (  # type: ignore
    AnyRoleIn,
    FPAuditLogEntryEvent,
    FPUser,
    validate_user,
)
from fp_aaa_kit.middleware import (
    AuditedSection,
    prepare_audited_section,
)
from jnapi_async import Jobs, Session, Settings  # type: ignore
from jnapi_async import exceptions as JNAPI_exceptions  # type: ignore
from jnapi_async.Module import ResponseMessage  # type: ignore

from ..config import Config, Regions
from ..exceptions import AccessDenied, NoSuchEntity
from ..models import JobId, JsoBucketsCleanupInfo, QuotaResponse, VaultAccountResponse
from ..providers import ProviderFactory
from ..queues_names import JSO_ACCOUNT_CLEANUP_QUEUE
from ..secrets import SecretsClient
from ..utils import Cache
from . import responses

logging.basicConfig(format="{levelname:7} {message}", style="{", level=logging.INFO)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

config = Config().get_config_parser()
router = APIRouter(prefix="/vault", redirect_slashes=False, tags=["vault"])

settings = Settings()  # type: ignore
session = Session(
    gateway_api_url=settings.GATEWAY_API_URL,
    identity_provider_url=settings.IDENTITY_PROVIDER_URL,  # type: ignore
    realm=settings.IDENTITY_PROVIDER_REALM,  # type: ignore
    client_id=settings.CLIENT_ID,
    client_secret=settings.CLIENT_SECRET,
)

cache = Cache()


def validate_service_id(service_id: Optional[str]) -> str:
    if service_id is None:
        raise ValueError("Service ID is required")
    return service_id


def handle_vault_errors(func: Callable):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except AccessDenied as e:
            return JSONResponse(
                content=ResponseMessage(str(e)).dict(),
                status_code=status.HTTP_403_FORBIDDEN,
            )
        except NoSuchEntity as e:
            return JSONResponse(
                content=ResponseMessage(str(e)).dict(),
                status_code=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            logger.exception(f"Operation failed: {str(e)}")
            return JSONResponse(
                content=ResponseMessage(str(e)).dict(),
                status_code=status.HTTP_400_BAD_REQUEST,
            )

    return wrapper


async def get_account_provider(
    region: Regions = Query(..., title="S3 Region"),
):
    """Get account provider for the specified region"""
    try:
        return ProviderFactory.get_account_provider(region)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Region metadata not found"
        )


@router.get(
    "/account/{account_name}",
    summary="GetVaultAccount",
    responses={
        422: {"model": Dict},
        404: {"model": Dict},
        403: {"model": Dict},
        400: {"model": Dict},
        200: {"model": List[VaultAccountResponse]},
    },
    status_code=200,
)
@handle_vault_errors
async def GetVaultAccount(
    region: Regions = Query(..., title="S3 Region"),
    starts_with: bool = Query(default=False, title="Use account_name as a prefix"),
    ends_with: bool = Query(default=False, title="Use account_name as a suffix"),
    account_name: str = Path(..., title="Account name"),
    _: FPUser = Depends(validate_user(AnyRoleIn("jso_admin_api_read"))),
):
    accounts_list = []
    key = {"account_name": account_name, "region": region}

    if not starts_with and not ends_with:
        if cache.hit(key):
            logger.debug(f"Cache hit for {key}")
            accounts_list.append(cache.get(key))
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=accounts_list,
            )
        else:
            logger.debug(f"Cache miss for {key}")

    account_provider = ProviderFactory.get_account_provider(region)

    start_time = time.time()

    # Use the provider's get_account method which handles filtering
    try:
        accounts_list = await account_provider.get_account(
            account_name, starts_with=starts_with, ends_with=ends_with
        )

        # Cache the result for direct matches
        if not starts_with and not ends_with and accounts_list:
            cache.set(key, accounts_list[0])

    except Exception:
        logger.exception(f"Failed to get account '{account_name}'")
        raise NoSuchEntity("Missing account")

    if len(accounts_list) == 0:
        raise NoSuchEntity("Missing account")

    current_time = time.time()
    logger.debug(
        f"Retrieved {len(accounts_list)} accounts in {str(current_time - start_time)} secs"
    )

    return JSONResponse(status_code=status.HTTP_200_OK, content=accounts_list)


@router.delete(
    "/account/{account_name}",
    summary="DeleteVaultAccount",
    responses={
        500: {"model": Dict},
        422: {"model": Dict},
        403: {"model": Dict},
        404: {"model": Dict},
        204: {"model": None},
    },
    status_code=204,
)
async def DeleteVaultAccount(
    remove_buckets: bool = Query(default=False, title="Force empty bucket removal"),
    region: Regions = Query(..., title="S3 Region"),
    account_name: str = Path(..., title="Account name to delete"),
    _: FPUser = Depends(validate_user(AnyRoleIn("jso_admin_api_write"))),
):
    try:
        # Get providers
        account_provider = ProviderFactory.get_account_provider(region)
        s3_provider = ProviderFactory.get_s3_provider(region)

        # Generate account access key
        root_keys = await account_provider.generate_account_access_key(account_name)

        # check if any bucket exists using S3 provider
        s3_client = s3_provider.get_s3_client(
            access_key=root_keys["accessKey"], secret_key=root_keys["secretKeyValue"]
        )

        response = s3_client.list_buckets()
        if len(response["Buckets"]) > 0:
            if remove_buckets is False:
                return JSONResponse(
                    status_code=status.HTTP_409_CONFLICT,
                    content=ResponseMessage(
                        "Buckets exist under this account, please remove the buckets first"
                    ).dict(),
                )
            else:
                for bucket in response["Buckets"]:
                    s3_client.delete_bucket(Bucket=bucket["Name"])

        # Get IAM provider with account credentials
        config = Config().get_config_parser()
        vault_host = config[region]["vault_host"]
        vault_port = config[region]["vault_port"]
        endpoint_url = f"https://{vault_host}:{vault_port}"

        iam_provider = ProviderFactory.get_iam_provider(
            region=region,
            endpoint_url=endpoint_url,
            access_key=root_keys["accessKey"],
            secret_access_key=root_keys["secretKeyValue"],
        )

        # Clean up IAM entities using IAM provider
        await iam_provider.cleanup_account_iam(
            access_key=root_keys["accessKey"], secret_key=root_keys["secretKeyValue"]
        )

        # depending entities should be gone, delete the account now
        await account_provider.delete_account(account_name)
    except (scality.exceptions.NoSuchEntity, NoSuchEntity) as e:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ResponseMessage(str(e)).dict(),
        )
    except Exception as e:
        logger.exception(f"Failed to delete account '{account_name}'")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=ResponseMessage(
                str(f"Failed to delete account '{account_name}': {str(e)}")
            ).dict(),
        )

    try:
        with SecretsClient(
            url=config["vault"]["url"],
            mount_point=config["vault"]["mount_point"],
            approle_id=config["vault"]["approle_id"],
            approle_secret_id=config["vault"]["approle_secret_id"],
            verify=config["vault"]["verify"],
        ) as client:
            client.destroy_secrets(
                path=f"/secret/data/{config['vault']['path_prefix']}/{region}/accounts/{account_name}",
            )
    except Exception:
        logger.exception(f"Failed to save account secrets for '{account_name}'")

    cache.rem({"account_name": account_name, "region": region})

    return Response(status_code=status.HTTP_204_NO_CONTENT)


@router.post(
    "/account/{account_name}/buckets/delete",
    summary="AccountBucketsCleanup",
    responses={
        500: {"model": Dict},
        403: {"model": Dict},
        422: {"model": Dict},
        201: {"model": JobId},
    },
    status_code=201,
)
async def AccountBucketsCleanup(
    region: Regions = Query(..., title="S3 Region"),
    dry_run: bool = Query(default=False, title="Dry run"),
    account_name: str = Path(..., title="The reference of the account to cleanup"),
    _: FPUser = Depends(validate_user(AnyRoleIn("jso_admin_api_write"))),
    audited_section: AuditedSection = Depends(prepare_audited_section()),
):
    """
    Empty and remove all buckets from an account (Async job)
    """

    service_id = f"jso_account_{account_name}"

    try:
        config[region]["s3_endpoint_url"]
    except Exception:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=ResponseMessage("Unknown region").dict(),
        )

    job = Jobs(session=session)
    try:
        with audited_section(
            action="cleanup",
            kind="account",
            what=account_name,
            default_event=FPAuditLogEntryEvent.ALLOWED,
        ):
            job_id = await job.create(
                queue=JSO_ACCOUNT_CLEANUP_QUEUE,
                service_id=service_id,
                service_type="JSO",
                data={
                    "region": region,
                    "account": account_name,
                    "dry_run": dry_run,
                },
            )
    except JNAPI_exceptions.ServiceLockedError:
        logger.exception(f"Service {service_id}: locked")
        return JSONResponse(
            status_code=status.HTTP_423_LOCKED,
            content=ResponseMessage(f"Service {service_id}: locked").dict(),
        )
    except Exception as e:
        logger.exception("Failed to create AccountBucketsCleanup job")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=ResponseMessage(str(e)).dict(),
        )

    # Return the job_id as a JSON response.
    return JSONResponse(
        status_code=status.HTTP_201_CREATED, content=JobId(job_id).dict()
    )


@router.get(
    "/account/{account_name}/buckets/delete",
    summary="AccountBucketsCleanupInfo",
    responses={**responses, 200: {"model": JsoBucketsCleanupInfo}},
    status_code=200,
)
async def AccountBucketsCleanupInfo(
    job_id: int,
    account_name: str = Path(..., title="The reference of the account to request"),
):
    """
    Get empty account status
    """

    if job_id <= 0:
        return JSONResponse(
            status_code=404,
            content=ResponseMessage(str("Invalid job id")).dict(),
        )

    try:
        jobs = Jobs(session=session)
        job = await jobs.get_job(job_id)
        validated_id = validate_service_id(job.service_id)

        if job.queue == JSO_ACCOUNT_CLEANUP_QUEUE:
            assert account_name == job.body["account"]

            if job.logs == []:
                return JSONResponse(
                    status_code=status.HTTP_200_OK,
                    content=JsoBucketsCleanupInfo(
                        validated_id,
                        account_name,
                        job.state.value,
                        {},  # type: ignore
                    ).dict(),
                )
    except Exception as e:
        logger.exception(f"Failed to get cleanup job: {job_id}")
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ResponseMessage(
                str(f"Failed to get job {job_id}: {str(e)}")
            ).dict(),
        )

    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=JsoBucketsCleanupInfo(
            validated_id,
            account_name,
            job.state.value,
            job.logs[-1],  # type: ignore
        ).dict(),
    )


@router.get(
    "/account/{account_name}/quota",
    summary="GetVaultAccountQuota",
    responses={
        422: {"model": Dict},
        404: {"model": Dict},
        403: {"model": Dict},
        400: {"model": Dict},
        200: {"model": QuotaResponse},
    },
    status_code=200,
)
@handle_vault_errors
async def GetVaultAccountQuota(
    account_provider=Depends(get_account_provider),
    account_name: str = Path(..., title="Account name"),
    _: FPUser = Depends(validate_user(AnyRoleIn("jso_admin_api_read"))),
):
    resp = await account_provider.get_account_quota(account_name)
    return QuotaResponse(**resp)


@router.put(
    "/account/{account_name}/quota",
    summary="UpdateVaultAccountQuota",
    responses={
        422: {"model": Dict},
        404: {"model": Dict},
        403: {"model": Dict},
        400: {"model": Dict},
        204: {"model": None},
    },
    status_code=204,
)
@handle_vault_errors
async def UpdateVaultAccountQuota(
    account_provider=Depends(get_account_provider),
    account_name: str = Path(..., title="Account name"),
    quota_bytes: int = Query(..., title="Quota in bytes", ge=1),
    _: FPUser = Depends(validate_user(AnyRoleIn("jso_admin_api_write"))),
):
    await account_provider.update_account_quota(account_name, quota_bytes)
    return Response(status_code=status.HTTP_204_NO_CONTENT)


@router.delete(
    "/account/{account_name}/quota",
    summary="DeleteVaultAccountQuota",
    responses={
        422: {"model": Dict},
        404: {"model": Dict},
        403: {"model": Dict},
        400: {"model": Dict},
        204: {"model": None},
    },
    status_code=204,
)
@handle_vault_errors
async def DeleteVaultAccountQuota(
    account_provider=Depends(get_account_provider),
    account_name: str = Path(..., title="Account name"),
    _: FPUser = Depends(validate_user(AnyRoleIn("jso_admin_api_write"))),
):
    await account_provider.delete_account_quota(account_name)
    return Response(status_code=status.HTTP_204_NO_CONTENT)
