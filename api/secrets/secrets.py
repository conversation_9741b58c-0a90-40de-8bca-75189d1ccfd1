from typing import Dict, Optional

import httpx
import hvac
from bs4 import BeautifulSoup

from ..config import Config

config = Config().get_config_parser()


class SecretsClient:
    def __init__(
        self,
        url: str,
        mount_point: str,
        approle_id: str,
        approle_secret_id: str,
        verify: str,
    ):
        self.url = url
        self.mount_point = mount_point
        self.approle_id = approle_id
        self.approle_secret_id = approle_secret_id
        self.verify = True if verify == "True" or verify == "true" else False
        self.client = self._create_client()

    def __enter__(self):
        self._authenticate()
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        self.client.logout()
        self.client.adapter.close()

    def _create_client(self) -> hvac.Client:
        return hvac.Client(url=self.url, verify=self.verify)

    def _authenticate(self) -> None:
        self.client.auth.approle.login(
            role_id=self.approle_id,
            secret_id=self.approle_secret_id,
        )

    def read_secrets(self, path: str) -> Dict:
        r = self.client.secrets.kv.v2.read_secret_version(
            path=path, mount_point=self.mount_point, raise_on_deleted_version=True
        )
        return r["data"]["data"]

    def save_secrets(self, path: str, secrets: Dict) -> None:
        self.client.secrets.kv.v2.create_or_update_secret(
            mount_point=self.mount_point,
            path=path,
            secret=secrets,
        )

    def destroy_secrets(self, path: str) -> None:
        self.client.secrets.kv.v2.delete_metadata_and_all_versions(
            mount_point=self.mount_point,
            path=path,
        )


def generate_shared_secret(secret: str) -> Optional[str]:
    payload = {"password": secret, "ttl": "Two Weeks"}
    res = httpx.post(url="https://sharesecret.freepro.com/", data=payload)
    if res.status_code == 200:
        soup = BeautifulSoup(res.text, "html.parser")
        input_elem = soup.find("input", {"id": "password-link"})
        if input_elem is None:
            return None

        value = input_elem.get("value")  # type: ignore
        if isinstance(value, str):
            return value


def generate_secret_path_for_user(username: str, region: str, account_name: str) -> str:
    if config["vault"].get("si_username", "") == username:
        secret_path = (
            f"{config['vault']['path_prefix']}/{region}/accounts/cust/{account_name}"
        )
    else:
        secret_path = (
            f"{config['vault']['path_prefix']}/{region}/accounts/corp/{account_name}"
        )
    return secret_path
